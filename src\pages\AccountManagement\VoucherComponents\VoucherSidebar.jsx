import { Table as AntTable, Typography } from 'antd';
import { Edit, FileText, Trash } from 'lucide-react';
import { FaNoteSticky, FaRegCalendar } from 'react-icons/fa6';
import { HiMiniIdentification } from 'react-icons/hi2';
import { TbCategory } from 'react-icons/tb';
import { useNavigate } from 'react-router-dom';
import RightSidebar from '../../../components/global/components/RightSidebar';
import Spinner from '../../../components/global/components/Spinner';
import Table from '../../../components/global/components/Table';
import Tooltip from '../../../components/global/components/ToolTip';
import { handlePdf } from '../../../helperFunction';
import { useGetVoucherByIdQuery } from '../../../slices/AccountManagement/voucherApiSlice';
import { useLazyGetPdfQuery } from '../../../slices/pdfApiSlice';
const { Title, Text } = Typography;



const VoucherSidebar = ({ openSidebar, setOpenSidebar, voucherId }) => {
  const navigate = useNavigate();
  const { data: voucher, isLoading: isVoucherLoading } = useGetVoucherByIdQuery(
    { id: voucherId },
    { skip: voucherId === undefined }
  );
  const [getPdf, { isFetching: isFetchingPdf }] = useLazyGetPdfQuery();
  const closeSidebar = () => {
    setOpenSidebar(false);
  };

  const ActionButton = ({
    onClick,
    disabled,
    icon,
    tooltip,
    color = 'text-gray-700',
  }) => {
    const Icon = icon;

    return (
      <button
        onClick={onClick}
        disabled={disabled}
        className={`p-2 rounded-full transition-all ${
          disabled
            ? 'opacity-50 cursor-not-allowed'
            : 'hover:bg-gray-100 active:bg-gray-200'
        } group relative`}
      >
        <Icon className={`w-5 h-5 ${color}`} />
        {tooltip && (
          <span className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 hidden group-hover:block bg-gray-800 text-white text-xs px-2 py-1 rounded whitespace-nowrap z-10">
            {tooltip}
          </span>
        )}
      </button>
    );
  };

  const calculateDebit = () => {
    let sum = 0;
    voucher?.journalVoucherData?.accounts?.forEach((elem) => (sum = sum + parseInt(elem?.debit || 0)));
    return sum;
  };

  const calculateCredit = () => {
    let sum = 0;
    voucher?.journalVoucherData?.accounts?.forEach((elem) => (sum = sum + parseInt(elem?.credit || 0)));
    return sum;
  };

  const totalDifference = () => {
    return calculateDebit() - calculateCredit();
  };


  const getVoucherSpecificData = () => {
    switch (voucher?.voucherType) {
      case 'purchaseVoucher':
        return voucher?.purchaseVoucherData;
      case 'salesVoucher':
        return voucher?.salesVoucherData;
      case 'paymentVoucher':
        return voucher?.paymentVoucherData;
      case 'receiptVoucher':
        return voucher?.receiptVoucherData;
      case 'journalVoucher':
        return voucher?.journalVoucherData;
      default:
        return {};
    }
  };


  return (
      <RightSidebar
        title="Voucher Details"
        onClose={closeSidebar}
        scale={736}
        openSideBar={openSidebar}
        setOpenSideBar={setOpenSidebar}
        isLoading={isVoucherLoading}
      >
        <div className="flex items-center justify-between mb-4">
          <h4>{voucher?.voucherId}</h4>
          <div className="flex items-center gap-2">
            <ActionButton
              icon={Edit}
              tooltip="Edit"
              onClick={() => {
                if (voucher?._id && voucher?.voucherType) {
                  navigate(
                    `/accountmanagement/voucher/edit/${voucher.voucherType}/${voucher._id}`
                  );
                  closeSidebar();
                }
              }}
            />
            <ActionButton
              icon={Trash}
              tooltip="Delete"
              color="text-red-500"
              onClick={() => {}}
            />
            {isFetchingPdf ? (
              <Spinner size={5} />
            ) : (
              <ActionButton
                icon={FileText}
                tooltip="Download PDF"
                color="text-yellow-600"
                onClick={() => handlePdf(getPdf, voucherId, 'voucher')}
                disabled={isFetchingPdf}
              />
            )}
          </div>
        </div>
        <hr />
        <div className="mt-4">
          <Title level={5} className="text-lg text-gray-600 mt-5">
            General Details
          </Title>
          <div className="grid gap-6 border rounded-lg p-4">
            <div className="flex items-center justify-between border-b pb-2">
              <Text className="flex items-center gap-3 text-gray-600">
                <HiMiniIdentification className="text-green-500 text-xl" />
                <span className="font-medium">VOUCHER TYPE</span>
              </Text>
              <Text className="text-gray-800 font-medium">
              {voucher?.voucherType?.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
              </Text>
            </div>

          {/* Voucher ID */}
          <div className="flex items-center justify-between border-b pb-2">
            <Text className="flex items-center gap-3 text-gray-600">
              <HiMiniIdentification className="text-blue-500 text-xl" />
              <span className="font-medium">VOUCHER ID</span>
            </Text>
            <Text className="text-gray-800 font-medium">
              {voucher?.purchaseVoucherId || voucher?.salesVoucherId || voucher?.paymentVoucherId || voucher?.receiptVoucherId || voucher?.journalVoucherId || '-'}
            </Text>
          </div>

            {voucher?.date !== undefined && (
              <div className="flex items-center justify-between border-b pb-2">
                <Text className="flex items-center gap-3 text-gray-600">
                  <FaRegCalendar className="text-indigo-500 text-xl" />
                  <span className="font-medium">DATE</span>
                </Text>
                <Text className="text-gray-800 font-medium">
                  {voucher?.date !== undefined &&
                    new Date(voucher?.date)?.toISOString()?.split('T')[0]}
                </Text>
              </div>
            )}
          {/* Vendor Information */}
          {(voucher?.voucherType === 'purchaseVoucher' || voucher?.voucherType === 'paymentVoucher') && (
            <div className="flex items-center justify-between border-b pb-2">
              <Text className="flex items-center gap-3 text-gray-600">
                <TbCategory className="text-purple-500 text-xl" />
                <span className="font-medium">VENDOR</span>
              </Text>
              <Text className="text-gray-800 font-medium">
                {getVoucherSpecificData()?.vendor?.name || voucher?.vendor?.name || '-'}
              </Text>
            </div>
          )}

          {/* Customer Information */}
          {(voucher?.voucherType === 'salesVoucher' || voucher?.voucherType === 'paymentVoucher') && (
            <div className="flex items-center justify-between border-b pb-2">
              <Text className="flex items-center gap-3 text-gray-600">
                <TbCategory className="text-green-500 text-xl" />
                <span className="font-medium">CUSTOMER/VENDOR</span>
              </Text>
              <Text className="text-gray-800 font-medium">
                {getVoucherSpecificData()?.customer?.name || getVoucherSpecificData()?.vendor?.name || '-'}
              </Text>
            </div>
          )}

          {/* Ledger Type */}
          {(voucher?.voucherType === 'purchaseVoucher' || voucher?.voucherType === 'salesVoucher' || voucher?.voucherType === 'paymentVoucher') && (
            <div className="flex items-center justify-between border-b pb-2">
              <Text className="flex items-center gap-3 text-gray-600">
                <TbCategory className="text-orange-500 text-xl" />
                <span className="font-medium">LEDGER TYPE</span>
              </Text>
              <Text className="text-gray-800 font-medium">
                {getVoucherSpecificData()?.ledgerType?.name || voucher?.ledgerType?.name || '-'}
              </Text>
            </div>
          )}
          {/* Receipt Voucher Specific Fields */}
            {voucher?.voucherType === 'receiptVoucher' && (
            <>
              <div className="flex items-center justify-between border-b pb-2">
                <Text className="flex items-center gap-3 text-gray-600">
                  <TbCategory className="text-cyan-500 text-xl" />
                  <span className="font-medium">PAYMENT TYPE</span>
                </Text>
                <Text className="text-gray-800 font-medium">
                  {voucher?.receiptVoucherData?.paymentType || '-'}
                </Text>
              </div>
              <div className="flex items-center justify-between border-b pb-2">
                <Text className="flex items-center gap-3 text-gray-600">
                  <TbCategory className="text-teal-500 text-xl" />
                  <span className="font-medium">SENDER</span>
                </Text>
                <Text className="text-gray-800 font-medium">
                  {voucher?.receiptVoucherData?.from || '-'}
                </Text>
              </div>
              <div className="flex items-center justify-between border-b pb-2">
                <Text className="flex items-center gap-3 text-gray-600">
                  <TbCategory className="text-pink-500 text-xl" />
                  <span className="font-medium">BILLED TO</span>
                </Text>
                <Text className="text-gray-800 font-medium">
                  {voucher?.receiptVoucherData?.billTo || '-'}
                </Text>
              </div>
            </>
            )}
          {/* Payment Voucher Specific Fields */}
            {voucher?.voucherType === 'paymentVoucher' && (
              <>
                <div className="flex items-center justify-between border-b pb-2">
                  <Text className="flex items-center gap-3 text-gray-600">
                  <TbCategory className="text-red-500 text-xl" />
                    <span className="font-medium">PAYMENT MODE</span>
                  </Text>
                  <Text className="text-gray-800 font-medium">
                    {voucher?.paymentVoucherData?.paymentMode || '-'}
                  </Text>
                </div>
                <div className="flex items-center justify-between border-b pb-2">
                  <Text className="flex items-center gap-3 text-gray-600">
                  <TbCategory className="text-yellow-500 text-xl" />
                    <span className="font-medium">PAID TO</span>
                  </Text>
                  <Text className="text-gray-800 font-medium">
                    {voucher?.paymentVoucherData?.paidTo || '-'}
                  </Text>
                </div>
                <div className="flex items-center justify-between border-b pb-2">
                  <Text className="flex items-center gap-3 text-gray-600">
                  <TbCategory className="text-green-600 text-xl" />
                    <span className="font-medium">AMOUNT</span>
                  </Text>
                  <Text className="text-gray-800 font-medium">
                  ₹{voucher?.paymentVoucherData?.amount !== undefined
                    ? Number(voucher?.paymentVoucherData?.amount).toLocaleString()
                      : '-'}
                  </Text>
                </div>
              </>
            )}

          {/* Journal Voucher Specific Fields */}
          {voucher?.voucherType === 'journalVoucher' && (
            <div className="flex items-center justify-between border-b pb-2">
              <Text className="flex items-center gap-3 text-gray-600">
                <TbCategory className="text-purple-600 text-xl" />
                <span className="font-medium">REFERENCE ID</span>
              </Text>
              <Text className="text-gray-800 font-medium">
                {voucher?.journalVoucherData?.referenceId || '-'}
              </Text>
            </div>
          )}

            <div className="flex items-center justify-between border-b pb-2">
              <Text className="flex items-center gap-3 text-gray-600">
                <FaNoteSticky className="text-blue-500 text-xl" />
              <span className="font-medium">REMARKS</span>
              </Text>
            {voucher?.remarks?.length > 15 ? (
                <Tooltip
                text={voucher?.remarks}
                  maxWidth={'!max-w-[500px]'}
                  minWidth={'!min-w-[250px]'}
                >
                {voucher?.remarks?.slice(0, 15) + '...'}
                </Tooltip>
              ) : (
                <Text className="font-medium text-gray-800">
                  {voucher?.remarks || '-'}
                </Text>
              )}
            </div>
          </div>
        </div>
      {voucher?.journalVoucherData?.accounts !== undefined && voucher?.journalVoucherData?.accounts?.length > 0 && (
          <div className="mt-4">
            <Title level={5} className="text-lg text-gray-600 mt-5">
            Account Details (Journal Voucher)
            </Title>
            <Table>
              <Table.Head>
                <Table.Row>
                  <Table.Th>Account</Table.Th>
                  <Table.Th>Description</Table.Th>
                  <Table.Th>Party Details</Table.Th>
                  <Table.Th>Credit</Table.Th>
                  <Table.Th>Debit</Table.Th>
                </Table.Row>
              </Table.Head>
              <Table.Body>
              {voucher?.journalVoucherData?.accounts?.map((elem, index) => (
                <Table.Row key={elem?._id || index}>
                  <Table.Td>{elem?.accountName || elem?.account?.name || '-'}</Table.Td>
                    <Table.Td>
                      <span>
                        {elem?.description?.length > 15 ? (
                          <Tooltip
                            text={elem?.description}
                            maxWidth={'!max-w-[500px]'}
                            minWidth={'!min-w-[250px]'}
                          >
                            {elem?.description?.slice(0, 15) + '...'}
                          </Tooltip>
                        ) : (
                          elem?.description || '-'
                        )}
                      </span>
                    </Table.Td>
                    <Table.Td>
                    {elem?.vendor?.name || '-'}
                    </Table.Td>
                  <Table.Td>₹{Number(elem?.credit || 0).toLocaleString()}</Table.Td>
                  <Table.Td>₹{Number(elem?.debit || 0).toLocaleString()}</Table.Td>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
            <div className="w-full mt-4">
              <div className="flex items-center justify-end gap-4">
                <p className="font-medium text-sm text-gray-500 w-[10rem] text-left">
                  Total Debit:
                </p>
                <p className="min-w-[5rem]">{calculateDebit()}</p>
              </div>
              <div className="flex items-center justify-end gap-4">
                <p className="font-medium text-sm text-gray-500 w-[10rem] text-left ">
                  Total Credit:
                </p>
                <p className="min-w-[5rem]">{calculateCredit()}</p>
              </div>
              <hr className="w-[15rem] ml-auto" />
              <div className="flex items-center justify-end gap-4">
                <p className="font-bold text-md w-[10rem] text-left ">
                  Total Difference:
                </p>
                <p
                  className={`min-w-[5rem] ${totalDifference() === 0 ? 'text-green-500' : 'text-red-500'}`}
                >
                  {totalDifference()}
                </p>
              </div>
            </div>
          </div>
        )}
      {getVoucherSpecificData()?.items !== undefined && getVoucherSpecificData()?.items?.length > 0 && (
          <div className="mt-4">
            <Title level={5} className="text-lg text-gray-600 mt-5">
            Item Details ({voucher?.voucherType?.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())})
            </Title>
            <AntTable
            dataSource={getVoucherSpecificData()?.items?.map((elem, idx) => ({
                key: elem?._id || idx,
                product:
                  elem?.product !== undefined || elem?.part !== undefined
                    ? elem?.product?.name || elem?.part?.name
                  : elem?.manualEntry || elem?.productName,
                uom:
                  elem?.product !== undefined || elem?.part !== undefined
                    ? elem?.product?.uom || elem?.part?.uom
                    : elem?.uom,
              rate: elem?.rate ? `₹${Number(elem.rate).toLocaleString()}` : '-',
              quantity: elem?.quantity || '-',
              discount: elem?.discount ? `${elem.discount}%` : '-',
              cgst: elem?.cgst ? `${elem.cgst}%` : '-',
              sgst: elem?.sgst ? `${elem.sgst}%` : '-',
              igst: elem?.igst ? `${elem.igst}%` : '-',
              amount: elem?.amount || elem?.totalAmount ? `₹${Number(elem.amount || elem.totalAmount).toLocaleString()}` : '-',
              }))}
              columns={[
                {
                  title: 'Product',
                  dataIndex: 'product',
                  key: 'product',
                  width: 150,
                  ellipsis: true
                },
                { title: 'UOM', dataIndex: 'uom', key: 'uom', width: 80 },
                { title: 'Rate', dataIndex: 'rate', key: 'rate', width: 100 },
                { title: 'Qty', dataIndex: 'quantity', key: 'quantity', width: 80 },
                { title: 'Discount', dataIndex: 'discount', key: 'discount', width: 90 },
                { title: 'CGST', dataIndex: 'cgst', key: 'cgst', width: 80 },
                { title: 'SGST', dataIndex: 'sgst', key: 'sgst', width: 80 },
                { title: 'IGST', dataIndex: 'igst', key: 'igst', width: 80 },
                { title: 'Amount', dataIndex: 'amount', key: 'amount', width: 120 },
              ]}
              pagination={false}
              size="small"
              bordered
            scroll={{ x: 800 }}
            />

          {/* Charges Summary */}
          {getVoucherSpecificData()?.charges && Object.keys(getVoucherSpecificData().charges).length > 0 && (
            <div className="mt-4 p-3 bg-gray-50 rounded-lg">
              <Title level={5} className="text-sm text-gray-600 mb-2">
                Additional Charges
              </Title>
              <div className="grid grid-cols-2 gap-2 text-sm">
                {Object.entries(getVoucherSpecificData().charges).map(([key, value]) => (
                  <div key={key} className="flex justify-between">
                    <span className="text-gray-600 capitalize">{key.replace(/([A-Z])/g, ' $1')}:</span>
                    <span className="font-medium">₹{Number(value || 0).toLocaleString()}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
          </div>
        )}
    </RightSidebar>
  );
};

export default VoucherSidebar;
